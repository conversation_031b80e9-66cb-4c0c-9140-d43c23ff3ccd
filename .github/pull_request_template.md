## Description

Please provide a brief description of the changes in this PR.

## Type of Change

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Code refactoring
- [ ] Performance improvement
- [ ] Other (please describe):

## Testing

- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas

## Backend Changes

- [ ] Database migrations included (if applicable)
- [ ] API endpoints updated/added
- [ ] Authentication/authorization changes
- [ ] Background tasks/workers modified
- [ ] Dependencies updated

## Frontend Changes

- [ ] UI/UX improvements
- [ ] New components added
- [ ] Routing changes
- [ ] State management updates
- [ ] Styling changes

## Deployment Considerations

- [ ] Environment variables need to be updated
- [ ] Database migrations need to be run
- [ ] Cache needs to be cleared
- [ ] Third-party services configuration required
- [ ] Infrastructure changes needed

## Screenshots/Videos

If applicable, add screenshots or videos to help explain your changes.

## Additional Notes

Add any other context about the pull request here.

## Checklist

- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

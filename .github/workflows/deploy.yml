name: Deploy

on:
  push:
    branches: [dev, main]
    tags: ['v*']
  pull_request:
    branches: [dev, main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  versioning:
    name: Generate Version
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      is_release: ${{ steps.version.outputs.is_release }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Generate version
        id: version
        run: |
          chmod +x ./version.sh
          
          if [[ "${{ github.ref }}" == "refs/heads/dev" ]]; then
            VERSION=$(QUIET=true ./version.sh dev)
            IS_RELEASE="false"
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            VERSION=$(QUIET=true ./version.sh release)
            IS_RELEASE="true"
          elif [[ "${{ github.ref }}" == refs/tags/v* ]]; then
            VERSION="${{ github.ref_name }}"
            IS_RELEASE="true"
          else
            VERSION=$(./version.sh current)-pr.${{ github.event.number }}
            IS_RELEASE="false"
          fi
          
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "is_release=$IS_RELEASE" >> $GITHUB_OUTPUT
          echo "Generated version: $VERSION"

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: versioning
    if: github.ref == 'refs/heads/dev' || github.event_name == 'pull_request'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Backend Tests
        run: |
          echo "🧪 Running backend tests..."
          echo "Version: ${{ needs.versioning.outputs.version }}"
          # Add your backend test commands here
          # For example: cd backend && python -m pytest
          
      - name: Run Frontend Tests  
        run: |
          echo "🧪 Running frontend tests..."
          # Add your frontend test commands here
          # For example: cd frontend && npm test

  build-and-push:
    name: Build and Push Images
    runs-on: ubuntu-latest
    needs: [versioning, test]
    if: always() && (needs.test.result == 'success' || needs.test.result == 'skipped')
    permissions:
      contents: read
      packages: write
    
    strategy:
      matrix:
        component: [backend, frontend]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.component }}
          tags: |
            type=raw,value=${{ needs.versioning.outputs.version }}
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
          labels: |
            org.opencontainers.image.version=${{ needs.versioning.outputs.version }}
            org.opencontainers.image.revision=${{ github.sha }}
            org.opencontainers.image.created=${{ github.event.head_commit.timestamp }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./${{ matrix.component }}
          file: ./${{ matrix.component }}/Dockerfile
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            VERSION=${{ needs.versioning.outputs.version }}
            BUILD_DATE=${{ github.event.head_commit.timestamp }}
            GIT_SHA=${{ github.sha }}

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/dev'
    environment: staging
    
    steps:
      - name: Deploy to staging
        run: |
          echo "🚀 Deploying to staging environment..."
          # Add your staging deployment commands here
          # For example, updating a Kubernetes deployment, 
          # or triggering a webhook to your staging server

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build-and-push
    if: startsWith(github.ref, 'refs/tags/v') || (github.ref == 'refs/heads/main' && github.event_name == 'push')
    environment: production
    
    steps:
      - name: Deploy to production
        run: |
          echo "🚀 Deploying to production environment..."
          # Add your production deployment commands here
          # For example, updating a Kubernetes deployment,
          # or triggering a webhook to your production server

  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: [versioning, deploy-production]
    if: startsWith(github.ref, 'refs/tags/v')
    permissions:
      contents: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Generate changelog
        id: changelog
        run: |
          # Generate changelog from git commits
          echo "CHANGELOG<<EOF" >> $GITHUB_OUTPUT
          git log --pretty=format:"- %s" $(git describe --tags --abbrev=0 HEAD~1)..HEAD >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Create Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref_name }}
          release_name: Release ${{ github.ref_name }}
          body: |
            ## What's Changed
            
            ${{ steps.changelog.outputs.CHANGELOG }}
            
            ## Docker Images
            
            - Backend: `${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:${{ github.ref_name }}`
            - Frontend: `${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:${{ github.ref_name }}`
            
            ## Version Information
            
            - Version: ${{ needs.versioning.outputs.version }}
            - Git SHA: ${{ github.sha }}
            - Build Date: ${{ github.event.head_commit.timestamp }}
          draft: false
          prerelease: false

  promote-to-main:
    name: Promote to Main
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/dev' && github.event_name == 'push'
    environment: 
      name: promote-to-main
      url: https://github.com/${{ github.repository }}/compare/main...dev
    
    steps:
      - name: Ready for Main Branch
        run: |
          echo "✅ Staging deployment successful!"
          echo "🚀 Ready to promote to main branch"
          echo "Create a PR from dev to main when ready for production"

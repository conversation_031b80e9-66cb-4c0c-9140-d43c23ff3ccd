# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log

# Build outputs
dist/
build/
.nuxt/
.next/
.vuepress/dist/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
README.md

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/

# Documentation
docs/
*.md

# Cache
.cache/
.parcel-cache/
.eslintcache

# Temporary files
*.tmp
*.temp

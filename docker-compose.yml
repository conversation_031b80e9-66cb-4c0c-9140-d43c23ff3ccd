services:
  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: unless-stopped
    volumes:
      - manga_storage:/app/storage
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - valkey
    env_file:
      - .env
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/docs"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - kuroibara-network

  # Frontend Vue.js application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - kuroibara-network

  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_DATABASE}
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME} -d ${DB_DATABASE}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - kuroibara-network

  # Valkey (Redis fork) for caching
  valkey:
    image: valkey/valkey:latest
    restart: unless-stopped
    volumes:
      - valkey_data:/data
    ports:
      - "6379:6379"
    command: ["valkey-server", "--appendonly", "yes"]
    healthcheck:
      test: ["CMD", "valkey-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - kuroibara-network

  # Mailhog for email testing
  mailhog:
    image: mailhog/mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI port
    networks:
      - kuroibara-network

networks:
  kuroibara-network:
    driver: bridge

volumes:
  postgres_data:
  valkey_data:
  manga_storage:

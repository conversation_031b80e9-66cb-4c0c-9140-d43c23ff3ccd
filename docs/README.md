# Documentation Index

Welcome to the Kuroibara documentation! This folder contains all the detailed guides and setup instructions for the project.

## 📚 Available Documentation

### Setup & Installation
- **[CI/CD Setup Guide](SETUP_CI_CD.md)** - Complete GitHub Actions CI/CD pipeline setup
- **[Automation Setup Guide](AUTOMATION_SETUP.md)** - Tools and automation for development workflow

### Development Guidelines
- **[Git Guidelines](GIT_GUIDELINES.md)** - Git workflow and contribution guidelines
- **[Git Automation](GIT_AUTOMATION.md)** - Automated git workflows and hooks
- **[Docker Automation](DOCKER_AUTOMATION.md)** - Docker development and deployment automation

### Project Information
- **[Versioning Guide](../VERSIONING.md)** - Semantic versioning and release management
- **[Changelog](../CHANGELOG.md)** - Project version history
- **[Main README](../README.md)** - Project overview and quick start

## 🔗 Quick Links

- [Backend Documentation](../backend/README.md)
- [Frontend Documentation](../frontend/README.md)
- [API Documentation](http://localhost:8000/api/docs) (when running locally)

## 🤝 Contributing

Before contributing, please read:
1. [Git Guidelines](GIT_GUIDELINES.md) - For commit standards and workflow
2. [Automation Setup](AUTOMATION_SETUP.md) - For development tools setup
3. [Main README](../README.md) - For project overview

## 📞 Support

If you need help with any of these guides, please:
- Check the [main README](../README.md) for basic setup
- Open an issue on GitHub for specific problems
- Review the [changelog](../CHANGELOG.md) for recent changes
